"use client"

import { useState } from "react"
import { ChatInterface } from "@/components/chat-interface"
import { Head<PERSON> } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { useChatHistory } from "@/hooks/use-chat-history"

export default function Home() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { conversations, currentConversation, createNewConversation, selectConversation } = useChatHistory()

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-950 dark:to-purple-950">
      <Sidebar
        open={sidebarOpen}
        onOpenChange={setSidebarOpen}
        conversations={conversations}
        currentConversation={currentConversation}
        onSelectConversation={selectConversation}
        onNewConversation={createNewConversation}
      />
      <div className="flex-1 flex flex-col md:ml-64">
        <Header onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <ChatInterface conversationId={currentConversation} />
      </div>
    </div>
  )
}
