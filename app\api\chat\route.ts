import { streamText } from "ai"
import { google } from "@ai-sdk/google"

export async function POST(req: Request) {
  const { messages, files } = await req.json()

  // Process files and add context to the system message
  let fileContext = ""
  if (files && files.length > 0) {
    fileContext = "\n\nDocument Context:\n"
    files.forEach((file: any) => {
      fileContext += `\n--- ${file.name} (${file.type}) ---\n${file.content}\n`
    })
  }

  const systemMessage = `You are <PERSON><PERSON><PERSON><PERSON>, a helpful and friendly AI assistant. You can communicate in multiple languages including English, Hindi, and Bengali. 
    
    When users write in Hindi, respond in Hindi. When they write in Bengali, respond in Bengali. When they write in English, respond in English.
    
    You can analyze documents, images, and other files that users upload. When referencing uploaded content, be specific about which document you're referring to.
    
    Be conversational, helpful, and engaging. Provide accurate information and assist users with their questions and tasks.
    
    ${fileContext ? `You have access to the following uploaded documents: ${fileContext}` : ""}`

  const result = await streamText({
    model: google("gemini-1.5-flash"),
    messages,
    system: systemMessage,
  })

  return result.toDataStreamResponse()
}
